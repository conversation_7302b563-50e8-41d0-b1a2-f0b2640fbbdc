# 调研配置示例文件
# 展示如何使用新的可配置调研参数

# ============================================================================
# 基础配置 - 必需
# ============================================================================

llm:
  primary:
    provider: "openai"
    model: "gpt-4o"
    api_key: "${OPENAI_API_KEY}"
    temperature: 0.1
    max_tokens: 8192
    timeout: 300

proxy:
  http_proxy: "${HTTP_PROXY:-http://127.0.0.1:8118/}"
  https_proxy: "${HTTPS_PROXY:-http://127.0.0.1:8118/}"
  no_proxy: "${NO_PROXY:-localhost,127.0.0.1}"

# ============================================================================
# 调研配置示例 - 解决硬编码问题
# ============================================================================

retrieval:
  search_engines:
    primary: "tavily"
    fallback: "duckduckgo"
    tavily_api_key: "${TAVILY_API_KEY:-}"
    max_results: 20

  # 调研配置 - 现在完全可配置！
  research:
    # 场景1：快速调研（默认配置）
    # 适用于：初步了解、快速概览
    max_search_results: 60        # 搜索结果数量
    max_source_list_size: 15      # 选择15个高质量源进行深度处理
    max_concurrent_requests: 5    # 并发请求数
    search_query_limit: 5         # 搜索查询数量
    primary_keywords_limit: 3     # 主要关键词数量
    technical_terms_limit: 2      # 技术术语数量
    english_keywords_limit: 2     # 英文关键词数量
    information_summary_limit: 10 # 信息摘要处理数量

# ============================================================================
# 不同场景的配置示例
# ============================================================================

# 场景2：深度调研配置
# 适用于：学术研究、详细分析、全面调研
# 取消注释以下配置来启用深度调研模式
# retrieval:
#   research:
#     max_search_results: 100       # 更多搜索结果
#     max_source_list_size: 30      # 处理30个源，获得更全面的信息
#     max_concurrent_requests: 8    # 更高并发，加快处理速度
#     search_query_limit: 8         # 更多搜索查询，覆盖更多角度
#     primary_keywords_limit: 5     # 使用更多主要关键词
#     technical_terms_limit: 4      # 更多技术术语
#     english_keywords_limit: 3     # 更多英文关键词
#     information_summary_limit: 15 # 处理更多结果生成摘要

# 场景3：快速调研配置
# 适用于：时间紧迫、简单主题、概念验证
# retrieval:
#   research:
#     max_search_results: 30        # 较少搜索结果
#     max_source_list_size: 8       # 只处理8个核心源
#     max_concurrent_requests: 3    # 较低并发，减少服务器压力
#     search_query_limit: 3         # 较少查询，聚焦核心问题
#     primary_keywords_limit: 2     # 较少关键词
#     technical_terms_limit: 1      # 最少技术术语
#     english_keywords_limit: 1     # 最少英文关键词
#     information_summary_limit: 6  # 处理较少结果

# 场景4：高性能配置
# 适用于：服务器性能强、网络条件好、需要快速完成
# retrieval:
#   research:
#     max_search_results: 80        # 中等搜索结果数量
#     max_source_list_size: 20      # 中等源数量
#     max_concurrent_requests: 15   # 高并发处理
#     search_query_limit: 6         # 中等查询数量
#     primary_keywords_limit: 4     # 较多关键词
#     technical_terms_limit: 3      # 较多技术术语
#     english_keywords_limit: 2     # 中等英文关键词
#     information_summary_limit: 12 # 中等摘要处理数量

# 场景5：保守配置
# 适用于：网络不稳定、服务器性能有限、避免过载
# retrieval:
#   research:
#     max_search_results: 40        # 较少搜索结果
#     max_source_list_size: 10      # 较少源数量
#     max_concurrent_requests: 2    # 低并发，避免过载
#     search_query_limit: 3         # 较少查询
#     primary_keywords_limit: 2     # 较少关键词
#     technical_terms_limit: 1      # 最少技术术语
#     english_keywords_limit: 1     # 最少英文关键词
#     information_summary_limit: 8  # 较少摘要处理

# ============================================================================
# 配置说明和最佳实践
# ============================================================================

# 配置原则：
# 1. 质量 vs 数量：更多源不一定更好，重点是质量
# 2. 性能 vs 覆盖度：高并发能提速，但要考虑目标网站压力
# 3. 时间 vs 深度：深度调研需要更多时间，根据需求平衡
# 4. 网络 vs 本地：网络条件影响并发数和超时设置

# 调优建议：
# - 首次使用建议用默认配置
# - 根据实际调研效果调整 max_source_list_size
# - 网络不稳定时降低 max_concurrent_requests
# - 调研主题复杂时增加 search_query_limit
# - 国际化主题时增加 english_keywords_limit

# 性能影响：
# - max_source_list_size: 直接影响处理时间和网络请求数
# - max_concurrent_requests: 影响并发性能和目标网站压力
# - search_query_limit: 影响搜索API调用次数和费用
# - 其他限制: 主要影响搜索策略和结果质量

# 成本考虑：
# - 搜索API调用：search_query_limit × max_results
# - LLM API调用：主要受 max_source_list_size 影响
# - 网络带宽：受并发数和源数量影响
# - 处理时间：所有参数都会影响总处理时间

# ============================================================================
# 故障排除
# ============================================================================

# 如果遇到问题：
# 1. 网络超时：降低 max_concurrent_requests
# 2. 结果质量差：增加 max_source_list_size
# 3. 处理太慢：降低 max_source_list_size 或增加 max_concurrent_requests
# 4. 搜索结果少：增加 search_query_limit 或各种关键词限制
# 5. 内存不足：降低所有数量限制

# 监控指标：
# - 缓存命中率：应该 > 30%
# - 网络成功率：应该 > 95%
# - 平均处理时间：根据源数量调整预期
# - 质量评分：应该保持在合理范围内
