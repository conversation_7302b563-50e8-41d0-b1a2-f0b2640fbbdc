#!/usr/bin/env python3
"""
硬编码修复验证脚本

验证所有硬编码常量已被配置项替换，确保符合项目规范
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.backend.config import Config, ResearchConfig
from src.backend.agents.requirement_analyst.workflows import (
    _prepare_search_queries, 
    _generate_source_list,
    _generate_information_summary
)
from src.backend.agents.requirement_analyst.models import SearchKeywords


def test_research_config():
    """测试调研配置模型"""
    print("=== 测试调研配置模型 ===")
    
    # 测试默认配置
    config = ResearchConfig()
    print(f"默认配置:")
    print(f"  max_search_results: {config.max_search_results}")
    print(f"  max_source_list_size: {config.max_source_list_size}")
    print(f"  max_concurrent_requests: {config.max_concurrent_requests}")
    print(f"  search_query_limit: {config.search_query_limit}")
    print(f"  primary_keywords_limit: {config.primary_keywords_limit}")
    print(f"  technical_terms_limit: {config.technical_terms_limit}")
    print(f"  english_keywords_limit: {config.english_keywords_limit}")
    print(f"  information_summary_limit: {config.information_summary_limit}")
    
    # 测试自定义配置
    custom_config = ResearchConfig(
        max_source_list_size=25,
        max_concurrent_requests=10,
        search_query_limit=8
    )
    print(f"\n自定义配置:")
    print(f"  max_source_list_size: {custom_config.max_source_list_size}")
    print(f"  max_concurrent_requests: {custom_config.max_concurrent_requests}")
    print(f"  search_query_limit: {custom_config.search_query_limit}")
    
    return config, custom_config


def test_search_queries_config():
    """测试搜索查询配置化"""
    print("\n=== 测试搜索查询配置化 ===")
    
    # 创建测试关键词
    keywords = SearchKeywords(
        primary_keywords=["人工智能", "机器学习", "深度学习", "神经网络", "自然语言处理"],
        technical_terms=["AI", "ML", "DL", "NLP", "CNN", "RNN"],
        english_keywords=["artificial intelligence", "machine learning", "deep learning", "neural network"]
    )
    
    # 测试默认配置
    print("使用默认配置:")
    queries_default = _prepare_search_queries(keywords)
    print(f"  生成查询数量: {len(queries_default)}")
    print(f"  查询列表: {queries_default}")
    
    # 测试自定义配置
    class MockConfig:
        class Retrieval:
            class Research:
                primary_keywords_limit = 5
                technical_terms_limit = 4
                english_keywords_limit = 3
                search_query_limit = 8
            research = Research()
        retrieval = Retrieval()
    
    mock_config = MockConfig()
    print("\n使用自定义配置:")
    queries_custom = _prepare_search_queries(keywords, mock_config)
    print(f"  生成查询数量: {len(queries_custom)}")
    print(f"  查询列表: {queries_custom}")
    
    return queries_default, queries_custom


def test_source_list_config():
    """测试信息源清单配置化"""
    print("\n=== 测试信息源清单配置化 ===")
    
    # 创建模拟搜索结果
    mock_results = []
    for i in range(30):
        mock_results.append({
            "title": f"测试文章 {i+1}",
            "url": f"https://example.com/article-{i+1}",
            "quality_score": 0.9 - (i * 0.02),  # 递减的质量分数
            "relevance_score": 0.8 - (i * 0.01),
            "content_summary": f"这是第{i+1}篇文章的摘要",
            "publication_date": "2024-01-01",
            "authors": [f"作者{i+1}"]
        })
    
    # 测试默认配置（15个源）
    print("使用默认配置:")
    source_list_default = _generate_source_list(mock_results)
    print(f"  选择源数量: {len(source_list_default)}")
    print(f"  第一个源: {source_list_default[0]['title']}")
    print(f"  最后一个源: {source_list_default[-1]['title']}")
    
    # 测试自定义配置（25个源）
    class MockConfig:
        class Retrieval:
            class Research:
                max_source_list_size = 25
            research = Research()
        retrieval = Retrieval()
    
    mock_config = MockConfig()
    print("\n使用自定义配置 (25个源):")
    source_list_custom = _generate_source_list(mock_results, mock_config)
    print(f"  选择源数量: {len(source_list_custom)}")
    print(f"  第一个源: {source_list_custom[0]['title']}")
    print(f"  最后一个源: {source_list_custom[-1]['title']}")
    
    return source_list_default, source_list_custom


def test_information_summary_config():
    """测试信息摘要配置化"""
    print("\n=== 测试信息摘要配置化 ===")
    
    # 创建模拟搜索结果
    mock_results = []
    for i in range(20):
        mock_results.append({
            "title": f"研究论文 {i+1}",
            "url": f"https://academic.com/paper-{i+1}",
            "quality_score": 0.9 - (i * 0.02),
            "content_summary": f"这是第{i+1}篇论文的研究内容摘要"
        })
    
    # 测试默认配置（处理10个结果）
    print("使用默认配置:")
    summary_default = _generate_information_summary(mock_results)
    print(f"  生成摘要: {summary_default[:100]}...")
    
    # 测试自定义配置（处理15个结果）
    class MockConfig:
        class Retrieval:
            class Research:
                information_summary_limit = 15
            research = Research()
        retrieval = Retrieval()
    
    mock_config = MockConfig()
    print("\n使用自定义配置 (处理15个结果):")
    summary_custom = _generate_information_summary(mock_results, mock_config)
    print(f"  生成摘要: {summary_custom[:100]}...")
    
    return summary_default, summary_custom


def test_config_validation():
    """测试配置验证"""
    print("\n=== 测试配置验证 ===")
    
    try:
        # 测试边界值
        config = ResearchConfig(
            max_source_list_size=5,   # 最小值
            max_concurrent_requests=20,  # 最大值
            search_query_limit=1      # 最小值
        )
        print("✅ 边界值配置验证通过")
        print(f"  max_source_list_size: {config.max_source_list_size}")
        print(f"  max_concurrent_requests: {config.max_concurrent_requests}")
        print(f"  search_query_limit: {config.search_query_limit}")
        
    except Exception as e:
        print(f"❌ 边界值配置验证失败: {e}")
    
    try:
        # 测试超出范围的值（应该失败）
        config = ResearchConfig(max_source_list_size=100)  # 超过最大值50
        print("❌ 超范围值验证失败：应该抛出异常")
    except Exception as e:
        print(f"✅ 超范围值验证通过：{e}")


def verify_no_hardcoded_constants():
    """验证代码中不再有硬编码常量"""
    print("\n=== 验证硬编码常量移除 ===")
    
    workflow_file = Path("src/backend/agents/requirement_analyst/workflows.py")
    
    if not workflow_file.exists():
        print("❌ 工作流文件不存在")
        return False
    
    content = workflow_file.read_text(encoding='utf-8')
    
    # 检查是否还有硬编码的数字
    hardcoded_patterns = [
        "results[:15]",  # 原来的硬编码
        "results[:10]",  # 信息摘要的硬编码
        "keywords.primary_keywords[:3]",  # 关键词限制硬编码
        "max_concurrent = 5",  # 并发数硬编码
    ]
    
    found_hardcoded = []
    for pattern in hardcoded_patterns:
        if pattern in content:
            found_hardcoded.append(pattern)
    
    if found_hardcoded:
        print("❌ 仍然存在硬编码常量:")
        for pattern in found_hardcoded:
            print(f"  - {pattern}")
        return False
    else:
        print("✅ 所有硬编码常量已移除")
        return True


def main():
    """主测试函数"""
    print("🚀 开始硬编码修复验证\n")
    
    try:
        # 测试配置模型
        test_research_config()
        
        # 测试各个函数的配置化
        test_search_queries_config()
        test_source_list_config()
        test_information_summary_config()
        
        # 测试配置验证
        test_config_validation()
        
        # 验证硬编码移除
        no_hardcode = verify_no_hardcoded_constants()
        
        print("\n✅ 硬编码修复验证完成!")
        print("\n📋 修复总结:")
        print("- ✅ 添加了 ResearchConfig 配置模型")
        print("- ✅ 所有数量限制都可通过配置调整")
        print("- ✅ 保持向后兼容性（默认值）")
        print("- ✅ 添加了配置验证和边界检查")
        print("- ✅ 符合项目 .augmentrules 规范")
        
        if no_hardcode:
            print("- ✅ 所有硬编码常量已移除")
        else:
            print("- ⚠️  仍有部分硬编码需要处理")
        
        print("\n🎯 现在用户可以通过配置文件调整:")
        print("- 信息源清单大小 (5-50个)")
        print("- 搜索结果数量 (10-200个)")
        print("- 并发请求数 (1-20个)")
        print("- 各种关键词数量限制")
        print("- 信息摘要处理数量")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
